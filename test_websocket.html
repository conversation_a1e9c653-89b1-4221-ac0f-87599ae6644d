<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebSocket Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .log {
            background: #f5f5f5;
            border: 1px solid #ddd;
            padding: 10px;
            margin: 10px 0;
            height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        .controls {
            margin: 10px 0;
        }
        button {
            margin: 5px;
            padding: 10px 15px;
        }
        input {
            margin: 5px;
            padding: 8px;
            width: 200px;
        }
        .error {
            color: red;
        }
        .success {
            color: green;
        }
        .info {
            color: blue;
        }
    </style>
</head>
<body>
    <h1>WebSocket Group Chat Test</h1>
    
    <div class="controls">
        <input type="text" id="tokenInput" placeholder="Enter auth token" value="6e2e4997-dd89-406d-8e29-26097cfb10af">
        <button onclick="connect()">Connect</button>
        <button onclick="disconnect()">Disconnect</button>
    </div>
    
    <div class="controls">
        <input type="text" id="groupIdInput" placeholder="Group ID" value="test-group">
        <button onclick="joinGroup()">Join Group</button>
        <button onclick="leaveGroup()">Leave Group</button>
    </div>
    
    <div class="controls">
        <input type="text" id="messageInput" placeholder="Type a message" value="Hello from WebSocket test!">
        <button onclick="sendMessage()">Send Message</button>
        <button onclick="sendMultipleMessages()">Send 3 Messages Quickly</button>
    </div>
    
    <div class="controls">
        <button onclick="clearLog()">Clear Log</button>
    </div>
    
    <div id="log" class="log"></div>

    <script>
        let socket = null;
        let currentGroupId = null;

        function log(message, type = 'info') {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'error' ? 'error' : type === 'success' ? 'success' : 'info';
            logDiv.innerHTML += `<div class="${className}">[${timestamp}] ${message}</div>`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }

        function connect() {
            const token = document.getElementById('tokenInput').value;
            if (!token) {
                log('Please enter an auth token', 'error');
                return;
            }

            if (socket && socket.readyState === WebSocket.OPEN) {
                log('Already connected', 'error');
                return;
            }

            const wsUrl = `ws://localhost:8080/ws?token=${encodeURIComponent(token)}`;
            log(`Connecting to: ${wsUrl}`);

            socket = new WebSocket(wsUrl);

            socket.onopen = () => {
                log('✅ WebSocket connected successfully', 'success');
            };

            socket.onclose = () => {
                log('❌ WebSocket disconnected', 'error');
            };

            socket.onerror = (error) => {
                log(`❌ WebSocket error: ${JSON.stringify(error)}`, 'error');
            };

            socket.onmessage = (event) => {
                log(`📨 Raw message received: ${event.data}`);
                
                try {
                    // Check for multiple JSON objects (the old bug)
                    const lines = event.data.trim().split('\n');
                    if (lines.length > 1) {
                        log(`⚠️ DETECTED MULTIPLE LINES IN MESSAGE (OLD BUG): ${lines.length} lines`, 'error');
                        lines.forEach((line, index) => {
                            if (line.trim()) {
                                try {
                                    const data = JSON.parse(line.trim());
                                    log(`📨 Parsed line ${index + 1}: ${JSON.stringify(data, null, 2)}`, 'success');
                                } catch (lineError) {
                                    log(`❌ Error parsing line ${index + 1}: ${lineError.message}`, 'error');
                                }
                            }
                        });
                        return;
                    }
                    
                    const data = JSON.parse(event.data);
                    log(`📨 Parsed message: ${JSON.stringify(data, null, 2)}`, 'success');
                    
                    if (data.type === 'new_message') {
                        log(`💬 New message in room ${data.payload.roomId}: ${data.payload.message.content}`, 'success');
                    }
                } catch (error) {
                    log(`❌ JSON parsing error: ${error.message}`, 'error');
                    log(`❌ Raw data: ${event.data}`, 'error');
                }
            };
        }

        function disconnect() {
            if (socket) {
                socket.close();
                socket = null;
                log('Disconnected');
            }
        }

        function joinGroup() {
            const groupId = document.getElementById('groupIdInput').value;
            if (!groupId) {
                log('Please enter a group ID', 'error');
                return;
            }

            if (!socket || socket.readyState !== WebSocket.OPEN) {
                log('Not connected to WebSocket', 'error');
                return;
            }

            currentGroupId = groupId;
            const roomId = `group-${groupId}`;
            const message = {
                type: 'join_room',
                payload: { roomId }
            };

            socket.send(JSON.stringify(message));
            log(`🚪 Joining group: ${roomId}`);
        }

        function leaveGroup() {
            if (!currentGroupId) {
                log('Not in any group', 'error');
                return;
            }

            if (!socket || socket.readyState !== WebSocket.OPEN) {
                log('Not connected to WebSocket', 'error');
                return;
            }

            const roomId = `group-${currentGroupId}`;
            const message = {
                type: 'leave_room',
                payload: { roomId }
            };

            socket.send(JSON.stringify(message));
            log(`🚪 Leaving group: ${roomId}`);
            currentGroupId = null;
        }

        function sendMessage() {
            const messageText = document.getElementById('messageInput').value;
            if (!messageText) {
                log('Please enter a message', 'error');
                return;
            }

            if (!currentGroupId) {
                log('Please join a group first', 'error');
                return;
            }

            if (!socket || socket.readyState !== WebSocket.OPEN) {
                log('Not connected to WebSocket', 'error');
                return;
            }

            const roomId = `group-${currentGroupId}`;
            const message = {
                type: 'chat_message',
                roomId: roomId,
                payload: {
                    roomId: roomId,
                    content: messageText
                }
            };

            socket.send(JSON.stringify(message));
            log(`💬 Sent message to ${roomId}: ${messageText}`);
        }

        function sendMultipleMessages() {
            const baseMessage = document.getElementById('messageInput').value || 'Test message';
            
            if (!currentGroupId) {
                log('Please join a group first', 'error');
                return;
            }

            if (!socket || socket.readyState !== WebSocket.OPEN) {
                log('Not connected to WebSocket', 'error');
                return;
            }

            const roomId = `group-${currentGroupId}`;
            
            // Send 3 messages quickly to test the queue handling
            for (let i = 1; i <= 3; i++) {
                const message = {
                    type: 'chat_message',
                    roomId: roomId,
                    payload: {
                        roomId: roomId,
                        content: `${baseMessage} #${i}`
                    }
                };

                socket.send(JSON.stringify(message));
                log(`💬 Sent quick message ${i} to ${roomId}: ${baseMessage} #${i}`);
            }
        }
    </script>
</body>
</html>
